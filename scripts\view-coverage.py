#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run tests with coverage and open the HTML report.
"""

import subprocess
import sys
import webbrowser
from pathlib import Path


def main():
    """Run tests with coverage and open the HTML report."""
    print("Running tests with coverage...")
    
    # Run tests with coverage
    result = subprocess.run([
        "uv", "run", "pytest", "tests/", "-v"
    ], cwd=Path(__file__).parent.parent)
    
    if result.returncode != 0:
        print("❌ Tests failed or coverage threshold not met!")
        sys.exit(1)
    
    # Open the HTML coverage report
    coverage_file = Path(__file__).parent.parent / "htmlcov" / "index.html"
    if coverage_file.exists():
        print(f"✅ Opening coverage report: {coverage_file}")
        webbrowser.open(f"file://{coverage_file.absolute()}")
    else:
        print("❌ Coverage report not found!")
        sys.exit(1)


if __name__ == "__main__":
    main()
